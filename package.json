{"name": "vibe-kanban", "version": "0.0.46", "private": true, "scripts": {"dev": "export FRONTEND_PORT=$(node scripts/setup-dev-environment.js frontend) && export BACKEND_PORT=$(node scripts/setup-dev-environment.js backend) && concurrently \"npm run backend:dev:watch\" \"npm run frontend:dev\"", "build": "npm run frontend:build && npm run backend:build", "build:single": "npm run frontend:build && npm run backend:build:single", "build:npm": "./build-npm-package.sh", "test:npm": "./test-npm-package.sh", "frontend:dev": "cd frontend && npm run dev -- --port ${FRONTEND_PORT:-3000} --open", "frontend:build": "cd frontend && npm run build", "cargo": "node scripts/cargo.js", "backend:dev": "BACKEND_PORT=$(node scripts/setup-dev-environment.js backend) npm run backend:dev:watch", "backend:dev:watch": "DISABLE_WORKTREE_ORPHAN_CLEANUP=1 npm run cargo -- watch -w backend -x 'run --manifest-path backend/Cargo.toml'", "backend:build": "npm run cargo -- build --release --manifest-path backend/Cargo.toml && npm run cargo -- build --release --bin mcp_task_server --manifest-path backend/Cargo.toml", "backend:build:single": "npm run cargo -- build --release --manifest-path backend/Cargo.toml", "backend:run": "npm run cargo -- run --manifest-path backend/Cargo.toml", "backend:test": "npm run cargo -- test --lib", "generate-types": "cd backend && cargo run --bin generate_types", "prepare-db": "node scripts/prepare-db.js", "dev:clear-ports": "node scripts/setup-dev-environment.js clear"}, "devDependencies": {"concurrently": "^8.2.2", "vite": "^6.3.5"}, "engines": {"node": ">=18", "pnpm": ">=8"}}