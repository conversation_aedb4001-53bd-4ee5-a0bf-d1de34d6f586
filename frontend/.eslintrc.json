{"root": true, "env": {"browser": true, "es2020": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:react-hooks/recommended", "prettier"], "ignorePatterns": ["dist", ".eslintrc.json"], "parser": "@typescript-eslint/parser", "plugins": ["react-refresh", "@typescript-eslint"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "rules": {"react-refresh/only-export-components": ["warn", {"allowConstantExport": true}], "@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-explicit-any": "warn"}}