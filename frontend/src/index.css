@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;

    /* Status colors */
    --success: 142.1 76.2% 36.3%;
    --success-foreground: 138.5 76.5% 96.7%;
    --warning: 32.2 95% 44.1%;
    --warning-foreground: 26 83.3% 14.1%;
    --info: 217.2 91.2% 59.8%;
    --info-foreground: 222.2 84% 4.9%;
    --neutral: 210 40% 96%;
    --neutral-foreground: 222.2 84% 4.9%;

    /* Status indicator colors */
    --status-init: 210 40% 96%;
    --status-init-foreground: 222.2 84% 4.9%;
    --status-running: 217.2 91.2% 59.8%;
    --status-running-foreground: 222.2 84% 4.9%;
    --status-complete: 142.1 76.2% 36.3%;
    --status-complete-foreground: 138.5 76.5% 96.7%;
    --status-failed: 0 84.2% 60.2%;
    --status-failed-foreground: 210 40% 98%;
    --status-paused: 32.2 95% 44.1%;
    --status-paused-foreground: 26 83.3% 14.1%;

    /* Console/terminal colors */
    --console-background: 222.2 84% 4.9%;
    --console-foreground: 210 40% 98%;
    --console-success: 138.5 76.5% 47.7%;
    --console-error: 0 84.2% 60.2%;
  }

  .purple {
    --background: 266 100% 6%;
    --foreground: 266 20% 95%;
    --card: 266 100% 6%;
    --card-foreground: 266 20% 95%;
    --popover: 266 100% 6%;
    --popover-foreground: 266 20% 95%;
    --primary: 266 80% 75%;
    --primary-foreground: 266 100% 6%;
    --secondary: 266 20% 15%;
    --secondary-foreground: 266 20% 95%;
    --muted: 266 20% 15%;
    --muted-foreground: 266 15% 65%;
    --accent: 266 20% 15%;
    --accent-foreground: 266 20% 95%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 266 20% 95%;
    --border: 266 20% 15%;
    --input: 266 20% 15%;
    --ring: 266 80% 75%;
  }

  .green {
    --background: 120 100% 6%;
    --foreground: 120 20% 95%;
    --card: 120 100% 6%;
    --card-foreground: 120 20% 95%;
    --popover: 120 100% 6%;
    --popover-foreground: 120 20% 95%;
    --primary: 120 80% 75%;
    --primary-foreground: 120 100% 6%;
    --secondary: 120 20% 15%;
    --secondary-foreground: 120 20% 95%;
    --muted: 120 20% 15%;
    --muted-foreground: 120 15% 65%;
    --accent: 120 20% 15%;
    --accent-foreground: 120 20% 95%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 120 20% 95%;
    --border: 120 20% 15%;
    --input: 120 20% 15%;
    --ring: 120 80% 75%;
  }

  .blue {
    --background: 210 100% 6%;
    --foreground: 210 20% 95%;
    --card: 210 100% 6%;
    --card-foreground: 210 20% 95%;
    --popover: 210 100% 6%;
    --popover-foreground: 210 20% 95%;
    --primary: 210 80% 75%;
    --primary-foreground: 210 100% 6%;
    --secondary: 210 20% 15%;
    --secondary-foreground: 210 20% 95%;
    --muted: 210 20% 15%;
    --muted-foreground: 210 15% 65%;
    --accent: 210 20% 15%;
    --accent-foreground: 210 20% 95%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 95%;
    --border: 210 20% 15%;
    --input: 210 20% 15%;
    --ring: 210 80% 75%;
  }

  .orange {
    --background: 30 100% 6%;
    --foreground: 30 20% 95%;
    --card: 30 100% 6%;
    --card-foreground: 30 20% 95%;
    --popover: 30 100% 6%;
    --popover-foreground: 30 20% 95%;
    --primary: 30 80% 75%;
    --primary-foreground: 30 100% 6%;
    --secondary: 30 20% 15%;
    --secondary-foreground: 30 20% 95%;
    --muted: 30 20% 15%;
    --muted-foreground: 30 15% 65%;
    --accent: 30 20% 15%;
    --accent-foreground: 30 20% 95%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 30 20% 95%;
    --border: 30 20% 15%;
    --input: 30 20% 15%;
    --ring: 30 80% 75%;
  }

  .red {
    --background: 0 100% 6%;
    --foreground: 0 20% 95%;
    --card: 0 100% 6%;
    --card-foreground: 0 20% 95%;
    --popover: 0 100% 6%;
    --popover-foreground: 0 20% 95%;
    --primary: 0 80% 75%;
    --primary-foreground: 0 100% 6%;
    --secondary: 0 20% 15%;
    --secondary-foreground: 0 20% 95%;
    --muted: 0 20% 15%;
    --muted-foreground: 0 15% 65%;
    --accent: 0 20% 15%;
    --accent-foreground: 0 20% 95%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 20% 95%;
    --border: 0 20% 15%;
    --input: 0 20% 15%;
    --ring: 0 80% 75%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;

    /* Status colors */
    --success: 138.5 76.5% 47.7%;
    --success-foreground: 138.5 76.5% 96.7%;
    --warning: 32.2 95% 44.1%;
    --warning-foreground: 26 83.3% 14.1%;
    --info: 217.2 91.2% 59.8%;
    --info-foreground: 222.2 84% 4.9%;
    --neutral: 217.2 32.6% 17.5%;
    --neutral-foreground: 210 40% 98%;

    /* Status indicator colors */
    --status-init: 217.2 32.6% 17.5%;
    --status-init-foreground: 210 40% 98%;
    --status-running: 217.2 91.2% 59.8%;
    --status-running-foreground: 222.2 84% 4.9%;
    --status-complete: 138.5 76.5% 47.7%;
    --status-complete-foreground: 138.5 76.5% 96.7%;
    --status-failed: 0 62.8% 30.6%;
    --status-failed-foreground: 210 40% 98%;
    --status-paused: 32.2 95% 44.1%;
    --status-paused-foreground: 26 83.3% 14.1%;

    /* Console/terminal colors */
    --console-background: 0 0% 0%;
    --console-foreground: 138.5 76.5% 47.7%;
    --console-success: 138.5 76.5% 47.7%;
    --console-error: 0 84.2% 60.2%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
