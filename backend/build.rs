use std::{fs, path::Path};

fn main() {
    dotenv::dotenv().ok();

    if let Ok(api_key) = std::env::var("POSTHOG_API_KEY") {
        println!("cargo:rustc-env=POSTHOG_API_KEY={}", api_key);
    }
    if let Ok(api_endpoint) = std::env::var("POSTHOG_API_ENDPOINT") {
        println!("cargo:rustc-env=POSTHOG_API_ENDPOINT={}", api_endpoint);
    }
    if let Ok(api_key) = std::env::var("GITHUB_APP_ID") {
        println!("cargo:rustc-env=GITHUB_APP_ID={}", api_key);
    }
    if let Ok(api_endpoint) = std::env::var("GITHUB_APP_CLIENT_ID") {
        println!("cargo:rustc-env=GITHUB_APP_CLIENT_ID={}", api_endpoint);
    }

    // Create frontend/dist directory if it doesn't exist
    let dist_path = Path::new("../frontend/dist");
    if !dist_path.exists() {
        println!("cargo:warning=Creating dummy frontend/dist directory for compilation");
        fs::create_dir_all(dist_path).unwrap();

        // Create a dummy index.html
        let dummy_html = r#"<!DOCTYPE html>
<html><head><title>Build frontend first</title></head>
<body><h1>Please build the frontend</h1></body></html>"#;

        fs::write(dist_path.join("index.html"), dummy_html).unwrap();
    }
}
