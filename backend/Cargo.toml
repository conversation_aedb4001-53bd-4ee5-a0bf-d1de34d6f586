[package]
name = "vibe-kanban"
version = "0.0.46"
edition = "2021"
default-run = "vibe-kanban"
build = "build.rs"

[lib]
name = "vibe_kanban"
path = "src/lib.rs"

[lints.clippy]
uninlined-format-args = "allow"

[dependencies]
tokio = { workspace = true }
axum = { workspace = true }
tower-http = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
anyhow = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
sqlx = { version = "0.8.6", features = ["runtime-tokio-rustls", "sqlite", "chrono", "uuid"] }
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.0", features = ["v4", "serde"] }
ts-rs = { version = "9.0", features = ["uuid-impl", "chrono-impl"] }
dirs = "5.0"
git2 = "0.18"
async-trait = "0.1"
libc = "0.2"
rust-embed = "8.2"
mime_guess = "2.0"
directories = "6.0.0"
open = "5.3.2"
ignore = "0.4"
command-group = { version = "5.0", features = ["with-tokio"] }
nix = { version = "0.29", features = ["signal", "process"] }
openssl-sys = { workspace = true }
rmcp = { version = "0.1.5", features = ["server", "transport-io"] }
schemars = "0.8"
regex = "1.11.1"
notify-rust = "4.11"
octocrab = "0.44"
os_info = "3.12.0"
sentry = { version = "0.41.0", features = ["anyhow", "backtrace", "panic", "debug-images"] }
sentry-tower = "0.41.0"
sentry-tracing = { version = "0.41.0", features = ["backtrace"] }
reqwest = { version = "0.11", features = ["json"] }
strip-ansi-escapes = "0.2.1"
urlencoding = "2.1.3"
lazy_static = "1.4"

[dev-dependencies]
tempfile = "3.8"

[build-dependencies]
dotenv = "0.15"
ts-rs = { version = "9.0", features = ["uuid-impl", "chrono-impl"] }

[profile.release]
debug = true
split-debuginfo = "packed"
strip = true
