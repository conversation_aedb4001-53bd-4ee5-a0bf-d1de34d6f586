{"db_name": "SQLite", "query": "SELECT  id                AS \"id!: Uuid\",\n                       task_id           AS \"task_id!: Uuid\",\n                       worktree_path,\n                       branch,\n                       merge_commit,\n                       base_branch,\n                       executor,\n                       pr_url,\n                       pr_number,\n                       pr_status,\n                       pr_merged_at      AS \"pr_merged_at: DateTime<Utc>\",\n                       worktree_deleted  AS \"worktree_deleted!: bool\",\n                       setup_completed_at AS \"setup_completed_at: DateTime<Utc>\",\n                       created_at        AS \"created_at!: DateTime<Utc>\",\n                       updated_at        AS \"updated_at!: DateTime<Utc>\"\n               FROM    task_attempts\n               WHERE   id = $1", "describe": {"columns": [{"name": "id!: <PERSON><PERSON>", "ordinal": 0, "type_info": "Blob"}, {"name": "task_id!: <PERSON><PERSON>", "ordinal": 1, "type_info": "Blob"}, {"name": "worktree_path", "ordinal": 2, "type_info": "Text"}, {"name": "branch", "ordinal": 3, "type_info": "Text"}, {"name": "merge_commit", "ordinal": 4, "type_info": "Text"}, {"name": "base_branch", "ordinal": 5, "type_info": "Text"}, {"name": "executor", "ordinal": 6, "type_info": "Text"}, {"name": "pr_url", "ordinal": 7, "type_info": "Text"}, {"name": "pr_number", "ordinal": 8, "type_info": "Integer"}, {"name": "pr_status", "ordinal": 9, "type_info": "Text"}, {"name": "pr_merged_at: DateTime<Utc>", "ordinal": 10, "type_info": "Datetime"}, {"name": "worktree_deleted!: bool", "ordinal": 11, "type_info": "Bool"}, {"name": "setup_completed_at: DateTime<Utc>", "ordinal": 12, "type_info": "Datetime"}, {"name": "created_at!: DateTime<Utc>", "ordinal": 13, "type_info": "Text"}, {"name": "updated_at!: DateTime<Utc>", "ordinal": 14, "type_info": "Text"}], "parameters": {"Right": 1}, "nullable": [true, false, false, false, true, false, true, true, true, true, true, false, true, false, false]}, "hash": "a6d2961718dbc3b1a925e549f49a159c561bef58c105529275f274b27e2eba5b"}