{"db_name": "SQLite", "query": "SELECT \n                ta.id as \"attempt_id!: Uuid\",\n                ta.task_id as \"task_id!: Uuid\",\n                ta.pr_number as \"pr_number!: i64\",\n                ta.pr_url,\n                t.project_id as \"project_id!: Uuid\",\n                p.git_repo_path\n               FROM task_attempts ta\n               JOIN tasks t ON ta.task_id = t.id  \n               JOIN projects p ON t.project_id = p.id\n               WHERE ta.pr_status = 'open' AND ta.pr_number IS NOT NULL", "describe": {"columns": [{"name": "attempt_id!: <PERSON><PERSON>", "ordinal": 0, "type_info": "Blob"}, {"name": "task_id!: <PERSON><PERSON>", "ordinal": 1, "type_info": "Blob"}, {"name": "pr_number!: i64", "ordinal": 2, "type_info": "Integer"}, {"name": "pr_url", "ordinal": 3, "type_info": "Text"}, {"name": "project_id!: Uuid", "ordinal": 4, "type_info": "Blob"}, {"name": "git_repo_path", "ordinal": 5, "type_info": "Text"}], "parameters": {"Right": 0}, "nullable": [true, false, true, true, false, false]}, "hash": "83d10e29f8478aff33434f9ac67068e013b888b953a2657e2bb72a6f619d04f2"}