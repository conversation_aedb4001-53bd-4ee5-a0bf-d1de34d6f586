{"db_name": "SQLite", "query": "\n            SELECT ta.id as \"attempt_id!: Uuid\", ta.worktree_path, p.git_repo_path as \"git_repo_path!\"\n            FROM task_attempts ta\n            LEFT JOIN execution_processes ep ON ta.id = ep.task_attempt_id AND ep.completed_at IS NOT NULL\n            JOIN tasks t ON ta.task_id = t.id\n            JOIN projects p ON t.project_id = p.id\n            WHERE ta.worktree_deleted = FALSE\n                -- Exclude attempts with any running processes (in progress)\n                AND ta.id NOT IN (\n                    SELECT DISTINCT ep2.task_attempt_id\n                    FROM execution_processes ep2\n                    WHERE ep2.completed_at IS NULL\n                )\n            GROUP BY ta.id, ta.worktree_path, p.git_repo_path, ta.updated_at\n            HAVING datetime('now', '-24 hours') > datetime(\n                MAX(\n                    CASE\n                        WHEN ep.completed_at IS NOT NULL THEN ep.completed_at\n                        ELSE ta.updated_at\n                    END\n                )\n            )\n            ORDER BY MAX(\n                CASE\n                    WHEN ep.completed_at IS NOT NULL THEN ep.completed_at\n                    ELSE ta.updated_at\n                END\n            ) ASC\n            ", "describe": {"columns": [{"name": "attempt_id!: <PERSON><PERSON>", "ordinal": 0, "type_info": "Blob"}, {"name": "worktree_path", "ordinal": 1, "type_info": "Text"}, {"name": "git_repo_path!", "ordinal": 2, "type_info": "Text"}], "parameters": {"Right": 0}, "nullable": [true, true, true]}, "hash": "212828320e8d871ab9d83705a040b23bcf0393dc7252177fc539a74657f578ef"}