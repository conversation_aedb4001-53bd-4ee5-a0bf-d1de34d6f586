{"db_name": "SQLite", "query": "INSERT INTO tasks (id, project_id, title, description, status) \n               VALUES ($1, $2, $3, $4, $5) \n               RETURNING id as \"id!: Uuid\", project_id as \"project_id!: Uuid\", title, description, status as \"status!: TaskStatus\", created_at as \"created_at!: DateTime<Utc>\", updated_at as \"updated_at!: DateTime<Utc>\"", "describe": {"columns": [{"name": "id!: <PERSON><PERSON>", "ordinal": 0, "type_info": "Blob"}, {"name": "project_id!: Uuid", "ordinal": 1, "type_info": "Blob"}, {"name": "title", "ordinal": 2, "type_info": "Text"}, {"name": "description", "ordinal": 3, "type_info": "Text"}, {"name": "status!: TaskStatus", "ordinal": 4, "type_info": "Text"}, {"name": "created_at!: DateTime<Utc>", "ordinal": 5, "type_info": "Text"}, {"name": "updated_at!: DateTime<Utc>", "ordinal": 6, "type_info": "Text"}], "parameters": {"Right": 5}, "nullable": [true, false, false, true, false, false, false]}, "hash": "9a893c35e2d5fa480396d5f403c3e9263217904dc3dfe43beca9c9541c4be619"}