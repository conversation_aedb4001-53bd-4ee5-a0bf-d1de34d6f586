{"db_name": "SQLite", "query": "UPDATE projects SET name = $2, git_repo_path = $3, setup_script = $4, dev_script = $5 WHERE id = $1 RETURNING id as \"id!: Uuid\", name, git_repo_path, setup_script, dev_script, created_at as \"created_at!: DateTime<Utc>\", updated_at as \"updated_at!: DateTime<Utc>\"", "describe": {"columns": [{"name": "id!: <PERSON><PERSON>", "ordinal": 0, "type_info": "Blob"}, {"name": "name", "ordinal": 1, "type_info": "Text"}, {"name": "git_repo_path", "ordinal": 2, "type_info": "Text"}, {"name": "setup_script", "ordinal": 3, "type_info": "Text"}, {"name": "dev_script", "ordinal": 4, "type_info": "Text"}, {"name": "created_at!: DateTime<Utc>", "ordinal": 5, "type_info": "Text"}, {"name": "updated_at!: DateTime<Utc>", "ordinal": 6, "type_info": "Text"}], "parameters": {"Right": 5}, "nullable": [true, false, false, true, true, false, false]}, "hash": "42c0c81bb893af019b5b91b48c3cb65557f770894e21e654303047d4150cca93"}