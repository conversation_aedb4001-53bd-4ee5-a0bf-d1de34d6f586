{"db_name": "SQLite", "query": "SELECT \n                id as \"id!: Uuid\", \n                task_attempt_id as \"task_attempt_id!: Uuid\", \n                execution_process_id as \"execution_process_id!: Uuid\", \n                session_id, \n                prompt,\n                summary,\n                created_at as \"created_at!: DateTime<Utc>\", \n                updated_at as \"updated_at!: DateTime<Utc>\"\n               FROM executor_sessions \n               WHERE id = $1", "describe": {"columns": [{"name": "id!: <PERSON><PERSON>", "ordinal": 0, "type_info": "Blob"}, {"name": "task_attempt_id!: Uuid", "ordinal": 1, "type_info": "Blob"}, {"name": "execution_process_id!: Uuid", "ordinal": 2, "type_info": "Blob"}, {"name": "session_id", "ordinal": 3, "type_info": "Text"}, {"name": "prompt", "ordinal": 4, "type_info": "Text"}, {"name": "summary", "ordinal": 5, "type_info": "Text"}, {"name": "created_at!: DateTime<Utc>", "ordinal": 6, "type_info": "Text"}, {"name": "updated_at!: DateTime<Utc>", "ordinal": 7, "type_info": "Text"}], "parameters": {"Right": 1}, "nullable": [true, false, false, true, true, true, false, false]}, "hash": "a31fff84f3b8e532fd1160447d89d700f06ae08821fee00c9a5b60492b05259c"}