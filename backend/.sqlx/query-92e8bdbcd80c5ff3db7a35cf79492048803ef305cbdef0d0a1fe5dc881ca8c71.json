{"db_name": "SQLite", "query": "SELECT  ta.id                AS \"id!: Uuid\",\n                       ta.task_id           AS \"task_id!: Uuid\",\n                       ta.worktree_path,\n                       ta.branch,\n                       ta.base_branch,\n                       ta.merge_commit,\n                       ta.executor,\n                       ta.pr_url,\n                       ta.pr_number,\n                       ta.pr_status,\n                       ta.pr_merged_at      AS \"pr_merged_at: DateTime<Utc>\",\n                       ta.worktree_deleted  AS \"worktree_deleted!: bool\",\n                       ta.setup_completed_at AS \"setup_completed_at: DateTime<Utc>\",\n                       ta.created_at        AS \"created_at!: DateTime<Utc>\",\n                       ta.updated_at        AS \"updated_at!: DateTime<Utc>\"\n               FROM    task_attempts ta\n               JOIN    tasks t ON ta.task_id = t.id\n               JOIN    projects p ON t.project_id = p.id\n               WHERE   ta.id = $1 AND t.id = $2 AND p.id = $3", "describe": {"columns": [{"name": "id!: <PERSON><PERSON>", "ordinal": 0, "type_info": "Blob"}, {"name": "task_id!: <PERSON><PERSON>", "ordinal": 1, "type_info": "Blob"}, {"name": "worktree_path", "ordinal": 2, "type_info": "Text"}, {"name": "branch", "ordinal": 3, "type_info": "Text"}, {"name": "base_branch", "ordinal": 4, "type_info": "Text"}, {"name": "merge_commit", "ordinal": 5, "type_info": "Text"}, {"name": "executor", "ordinal": 6, "type_info": "Text"}, {"name": "pr_url", "ordinal": 7, "type_info": "Text"}, {"name": "pr_number", "ordinal": 8, "type_info": "Integer"}, {"name": "pr_status", "ordinal": 9, "type_info": "Text"}, {"name": "pr_merged_at: DateTime<Utc>", "ordinal": 10, "type_info": "Datetime"}, {"name": "worktree_deleted!: bool", "ordinal": 11, "type_info": "Bool"}, {"name": "setup_completed_at: DateTime<Utc>", "ordinal": 12, "type_info": "Datetime"}, {"name": "created_at!: DateTime<Utc>", "ordinal": 13, "type_info": "Text"}, {"name": "updated_at!: DateTime<Utc>", "ordinal": 14, "type_info": "Text"}], "parameters": {"Right": 3}, "nullable": [true, false, false, false, false, true, true, true, true, true, true, false, true, false, false]}, "hash": "92e8bdbcd80c5ff3db7a35cf79492048803ef305cbdef0d0a1fe5dc881ca8c71"}