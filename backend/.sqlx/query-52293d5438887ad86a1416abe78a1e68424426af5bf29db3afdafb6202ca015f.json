{"db_name": "SQLite", "query": "INSERT INTO task_attempt_activities (id, execution_process_id, status, note) \n               VALUES ($1, $2, $3, $4) \n               RETURNING id as \"id!: Uuid\", execution_process_id as \"execution_process_id!: Uuid\", status as \"status!: TaskAttemptStatus\", note, created_at as \"created_at!: DateTime<Utc>\"", "describe": {"columns": [{"name": "id!: <PERSON><PERSON>", "ordinal": 0, "type_info": "Text"}, {"name": "execution_process_id!: Uuid", "ordinal": 1, "type_info": "Text"}, {"name": "status!: TaskAttemptStatus", "ordinal": 2, "type_info": "Text"}, {"name": "note", "ordinal": 3, "type_info": "Text"}, {"name": "created_at!: DateTime<Utc>", "ordinal": 4, "type_info": "Datetime"}], "parameters": {"Right": 4}, "nullable": [true, false, false, true, false]}, "hash": "52293d5438887ad86a1416abe78a1e68424426af5bf29db3afdafb6202ca015f"}