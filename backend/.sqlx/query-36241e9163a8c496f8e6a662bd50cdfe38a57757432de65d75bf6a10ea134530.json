{"db_name": "SQLite", "query": "UPDATE tasks \n               SET title = $3, description = $4, status = $5 \n               WHERE id = $1 AND project_id = $2 \n               RETURNING id as \"id!: Uuid\", project_id as \"project_id!: Uuid\", title, description, status as \"status!: TaskStatus\", created_at as \"created_at!: DateTime<Utc>\", updated_at as \"updated_at!: DateTime<Utc>\"", "describe": {"columns": [{"name": "id!: <PERSON><PERSON>", "ordinal": 0, "type_info": "Blob"}, {"name": "project_id!: Uuid", "ordinal": 1, "type_info": "Blob"}, {"name": "title", "ordinal": 2, "type_info": "Text"}, {"name": "description", "ordinal": 3, "type_info": "Text"}, {"name": "status!: TaskStatus", "ordinal": 4, "type_info": "Text"}, {"name": "created_at!: DateTime<Utc>", "ordinal": 5, "type_info": "Text"}, {"name": "updated_at!: DateTime<Utc>", "ordinal": 6, "type_info": "Text"}], "parameters": {"Right": 5}, "nullable": [true, false, false, true, false, false, false]}, "hash": "36241e9163a8c496f8e6a662bd50cdfe38a57757432de65d75bf6a10ea134530"}