{"db_name": "SQLite", "query": "INSERT INTO executor_sessions (\n                id, task_attempt_id, execution_process_id, session_id, prompt, summary,\n                created_at, updated_at\n               )\n               VALUES ($1, $2, $3, $4, $5, $6, $7, $8)\n               RETURNING\n                id as \"id!: Uuid\",\n                task_attempt_id as \"task_attempt_id!: Uuid\",\n                execution_process_id as \"execution_process_id!: Uuid\",\n                session_id,\n                prompt,\n                summary,\n                created_at as \"created_at!: DateTime<Utc>\",\n                updated_at as \"updated_at!: DateTime<Utc>\"", "describe": {"columns": [{"name": "id!: <PERSON><PERSON>", "ordinal": 0, "type_info": "Blob"}, {"name": "task_attempt_id!: Uuid", "ordinal": 1, "type_info": "Blob"}, {"name": "execution_process_id!: Uuid", "ordinal": 2, "type_info": "Blob"}, {"name": "session_id", "ordinal": 3, "type_info": "Text"}, {"name": "prompt", "ordinal": 4, "type_info": "Text"}, {"name": "summary", "ordinal": 5, "type_info": "Text"}, {"name": "created_at!: DateTime<Utc>", "ordinal": 6, "type_info": "Text"}, {"name": "updated_at!: DateTime<Utc>", "ordinal": 7, "type_info": "Text"}], "parameters": {"Right": 8}, "nullable": [true, false, false, true, true, true, false, false]}, "hash": "5a886026d75d515c01f347cc203c8d99dd04c61dc468e2e4c5aa548436d13834"}