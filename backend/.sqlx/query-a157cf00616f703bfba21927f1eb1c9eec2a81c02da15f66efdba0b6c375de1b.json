{"db_name": "SQLite", "query": "SELECT id as \"id!: Uuid\", worktree_path FROM task_attempts WHERE worktree_deleted = FALSE", "describe": {"columns": [{"name": "id!: <PERSON><PERSON>", "ordinal": 0, "type_info": "Blob"}, {"name": "worktree_path", "ordinal": 1, "type_info": "Text"}], "parameters": {"Right": 0}, "nullable": [true, false]}, "hash": "a157cf00616f703bfba21927f1eb1c9eec2a81c02da15f66efdba0b6c375de1b"}