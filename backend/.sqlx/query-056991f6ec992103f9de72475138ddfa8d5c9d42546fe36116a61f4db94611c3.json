{"db_name": "SQLite", "query": "SELECT id as \"id!: Uuid\", name, git_repo_path, setup_script, dev_script, created_at as \"created_at!: DateTime<Utc>\", updated_at as \"updated_at!: DateTime<Utc>\" FROM projects WHERE git_repo_path = $1", "describe": {"columns": [{"name": "id!: <PERSON><PERSON>", "ordinal": 0, "type_info": "Blob"}, {"name": "name", "ordinal": 1, "type_info": "Text"}, {"name": "git_repo_path", "ordinal": 2, "type_info": "Text"}, {"name": "setup_script", "ordinal": 3, "type_info": "Text"}, {"name": "dev_script", "ordinal": 4, "type_info": "Text"}, {"name": "created_at!: DateTime<Utc>", "ordinal": 5, "type_info": "Text"}, {"name": "updated_at!: DateTime<Utc>", "ordinal": 6, "type_info": "Text"}], "parameters": {"Right": 1}, "nullable": [true, false, false, true, true, false, false]}, "hash": "056991f6ec992103f9de72475138ddfa8d5c9d42546fe36116a61f4db94611c3"}