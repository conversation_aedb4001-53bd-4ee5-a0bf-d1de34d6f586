{"db_name": "SQLite", "query": "SELECT DISTINCT ep.id as \"id!: Uuid\"\n               FROM execution_processes ep\n               INNER JOIN (\n                   SELECT execution_process_id, MAX(created_at) as latest_created_at\n                   FROM task_attempt_activities\n                   GROUP BY execution_process_id\n               ) latest_activity ON ep.id = latest_activity.execution_process_id\n               INNER JOIN task_attempt_activities taa ON ep.id = taa.execution_process_id \n                   AND taa.created_at = latest_activity.latest_created_at\n               WHERE taa.status IN ($1, $2)", "describe": {"columns": [{"name": "id!: <PERSON><PERSON>", "ordinal": 0, "type_info": "Blob"}], "parameters": {"Right": 2}, "nullable": [true]}, "hash": "b029d6c1a0ccbd90e54c0e948dbb5623c650f935918e65ec34644210be44c0b1"}