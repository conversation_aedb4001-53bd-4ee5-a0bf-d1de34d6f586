{"db_name": "SQLite", "query": "SELECT id as \"id!: Uuid\", execution_process_id as \"execution_process_id!: Uuid\", status as \"status!: TaskAttemptStatus\", note, created_at as \"created_at!: DateTime<Utc>\"\n               FROM task_attempt_activities \n               WHERE execution_process_id = $1 \n               ORDER BY created_at DESC", "describe": {"columns": [{"name": "id!: <PERSON><PERSON>", "ordinal": 0, "type_info": "Text"}, {"name": "execution_process_id!: Uuid", "ordinal": 1, "type_info": "Text"}, {"name": "status!: TaskAttemptStatus", "ordinal": 2, "type_info": "Text"}, {"name": "note", "ordinal": 3, "type_info": "Text"}, {"name": "created_at!: DateTime<Utc>", "ordinal": 4, "type_info": "Datetime"}], "parameters": {"Right": 1}, "nullable": [true, false, false, true, false]}, "hash": "cb56bae2e03ef702cd8737f072d9c73957cc04695741fd7788370dfd9184548a"}