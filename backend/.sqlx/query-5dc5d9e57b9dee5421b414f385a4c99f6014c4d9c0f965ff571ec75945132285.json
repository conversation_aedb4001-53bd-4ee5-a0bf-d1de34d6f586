{"db_name": "SQLite", "query": "INSERT INTO projects (id, name, git_repo_path, setup_script, dev_script) VALUES ($1, $2, $3, $4, $5) RETURNING id as \"id!: Uuid\", name, git_repo_path, setup_script, dev_script, created_at as \"created_at!: DateTime<Utc>\", updated_at as \"updated_at!: DateTime<Utc>\"", "describe": {"columns": [{"name": "id!: <PERSON><PERSON>", "ordinal": 0, "type_info": "Blob"}, {"name": "name", "ordinal": 1, "type_info": "Text"}, {"name": "git_repo_path", "ordinal": 2, "type_info": "Text"}, {"name": "setup_script", "ordinal": 3, "type_info": "Text"}, {"name": "dev_script", "ordinal": 4, "type_info": "Text"}, {"name": "created_at!: DateTime<Utc>", "ordinal": 5, "type_info": "Text"}, {"name": "updated_at!: DateTime<Utc>", "ordinal": 6, "type_info": "Text"}], "parameters": {"Right": 5}, "nullable": [true, false, false, true, true, false, false]}, "hash": "5dc5d9e57b9dee5421b414f385a4c99f6014c4d9c0f965ff571ec75945132285"}