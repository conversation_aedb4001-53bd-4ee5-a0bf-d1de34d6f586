{"db_name": "SQLite", "query": "SELECT \n                taa.id as \"activity_id!: Uuid\",\n                taa.execution_process_id as \"execution_process_id!: Uuid\",\n                taa.status as \"status!: TaskAttemptStatus\",\n                taa.note,\n                taa.created_at as \"created_at!: DateTime<Utc>\",\n                es.prompt\n               FROM task_attempt_activities taa\n               INNER JOIN execution_processes ep ON taa.execution_process_id = ep.id\n               LEFT JOIN executor_sessions es ON es.execution_process_id = ep.id\n               WHERE ep.task_attempt_id = $1\n               ORDER BY taa.created_at ASC", "describe": {"columns": [{"name": "activity_id!: Uuid", "ordinal": 0, "type_info": "Text"}, {"name": "execution_process_id!: Uuid", "ordinal": 1, "type_info": "Text"}, {"name": "status!: TaskAttemptStatus", "ordinal": 2, "type_info": "Text"}, {"name": "note", "ordinal": 3, "type_info": "Text"}, {"name": "created_at!: DateTime<Utc>", "ordinal": 4, "type_info": "Datetime"}, {"name": "prompt", "ordinal": 5, "type_info": "Text"}], "parameters": {"Right": 1}, "nullable": [true, false, false, true, false, true]}, "hash": "bd57e47989627ac5e9f44e83f4bacff590d357586b149c7c17ff0e8f349e7025"}