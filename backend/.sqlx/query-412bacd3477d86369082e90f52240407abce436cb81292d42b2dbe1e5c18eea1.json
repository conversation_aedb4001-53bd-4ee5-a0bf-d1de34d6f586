{"db_name": "SQLite", "query": "SELECT \n                ep.id as \"id!: Uuid\", \n                ep.task_attempt_id as \"task_attempt_id!: Uuid\", \n                ep.process_type as \"process_type!: ExecutionProcessType\",\n                ep.executor_type,\n                ep.status as \"status!: ExecutionProcessStatus\",\n                ep.command, \n                ep.args, \n                ep.working_directory, \n                ep.stdout, \n                ep.stderr, \n                ep.exit_code,\n                ep.started_at as \"started_at!: DateTime<Utc>\",\n                ep.completed_at as \"completed_at?: DateTime<Utc>\",\n                ep.created_at as \"created_at!: DateTime<Utc>\", \n                ep.updated_at as \"updated_at!: DateTime<Utc>\"\n               FROM execution_processes ep\n               JOIN task_attempts ta ON ep.task_attempt_id = ta.id\n               JOIN tasks t ON ta.task_id = t.id\n               WHERE ep.status = 'running' \n               AND ep.process_type = 'devserver'\n               AND t.project_id = $1\n               ORDER BY ep.created_at ASC", "describe": {"columns": [{"name": "id!: <PERSON><PERSON>", "ordinal": 0, "type_info": "Blob"}, {"name": "task_attempt_id!: Uuid", "ordinal": 1, "type_info": "Blob"}, {"name": "process_type!: ExecutionProcessType", "ordinal": 2, "type_info": "Text"}, {"name": "executor_type", "ordinal": 3, "type_info": "Text"}, {"name": "status!: ExecutionProcessStatus", "ordinal": 4, "type_info": "Text"}, {"name": "command", "ordinal": 5, "type_info": "Text"}, {"name": "args", "ordinal": 6, "type_info": "Text"}, {"name": "working_directory", "ordinal": 7, "type_info": "Text"}, {"name": "stdout", "ordinal": 8, "type_info": "Text"}, {"name": "stderr", "ordinal": 9, "type_info": "Text"}, {"name": "exit_code", "ordinal": 10, "type_info": "Integer"}, {"name": "started_at!: DateTime<Utc>", "ordinal": 11, "type_info": "Text"}, {"name": "completed_at?: DateTime<Utc>", "ordinal": 12, "type_info": "Text"}, {"name": "created_at!: DateTime<Utc>", "ordinal": 13, "type_info": "Text"}, {"name": "updated_at!: DateTime<Utc>", "ordinal": 14, "type_info": "Text"}], "parameters": {"Right": 1}, "nullable": [true, false, false, true, false, false, true, false, true, true, true, false, true, false, false]}, "hash": "412bacd3477d86369082e90f52240407abce436cb81292d42b2dbe1e5c18eea1"}