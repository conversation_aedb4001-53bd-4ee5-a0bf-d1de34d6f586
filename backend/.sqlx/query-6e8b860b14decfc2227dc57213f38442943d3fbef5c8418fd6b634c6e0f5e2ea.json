{"db_name": "SQLite", "query": "INSERT INTO task_attempts (id, task_id, worktree_path, branch, base_branch, merge_commit, executor, pr_url, pr_number, pr_status, pr_merged_at, worktree_deleted, setup_completed_at)\n               VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)\n               RETURNING id as \"id!: Uuid\", task_id as \"task_id!: Uuid\", worktree_path, branch, base_branch, merge_commit, executor, pr_url, pr_number, pr_status, pr_merged_at as \"pr_merged_at: DateTime<Utc>\", worktree_deleted as \"worktree_deleted!: bool\", setup_completed_at as \"setup_completed_at: DateTime<Utc>\", created_at as \"created_at!: DateTime<Utc>\", updated_at as \"updated_at!: DateTime<Utc>\"", "describe": {"columns": [{"name": "id!: <PERSON><PERSON>", "ordinal": 0, "type_info": "Blob"}, {"name": "task_id!: <PERSON><PERSON>", "ordinal": 1, "type_info": "Blob"}, {"name": "worktree_path", "ordinal": 2, "type_info": "Text"}, {"name": "branch", "ordinal": 3, "type_info": "Text"}, {"name": "base_branch", "ordinal": 4, "type_info": "Text"}, {"name": "merge_commit", "ordinal": 5, "type_info": "Text"}, {"name": "executor", "ordinal": 6, "type_info": "Text"}, {"name": "pr_url", "ordinal": 7, "type_info": "Text"}, {"name": "pr_number", "ordinal": 8, "type_info": "Integer"}, {"name": "pr_status", "ordinal": 9, "type_info": "Text"}, {"name": "pr_merged_at: DateTime<Utc>", "ordinal": 10, "type_info": "Datetime"}, {"name": "worktree_deleted!: bool", "ordinal": 11, "type_info": "Bool"}, {"name": "setup_completed_at: DateTime<Utc>", "ordinal": 12, "type_info": "Datetime"}, {"name": "created_at!: DateTime<Utc>", "ordinal": 13, "type_info": "Text"}, {"name": "updated_at!: DateTime<Utc>", "ordinal": 14, "type_info": "Text"}], "parameters": {"Right": 13}, "nullable": [true, false, false, false, false, true, true, true, true, true, true, false, true, false, false]}, "hash": "6e8b860b14decfc2227dc57213f38442943d3fbef5c8418fd6b634c6e0f5e2ea"}