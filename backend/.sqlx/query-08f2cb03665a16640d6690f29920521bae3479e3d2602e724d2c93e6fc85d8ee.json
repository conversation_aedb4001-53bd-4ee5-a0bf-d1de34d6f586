{"db_name": "SQLite", "query": "SELECT id as \"id!: Uuid\", name, git_repo_path, setup_script, dev_script, created_at as \"created_at!: DateTime<Utc>\", updated_at as \"updated_at!: DateTime<Utc>\" FROM projects ORDER BY created_at DESC", "describe": {"columns": [{"name": "id!: <PERSON><PERSON>", "ordinal": 0, "type_info": "Blob"}, {"name": "name", "ordinal": 1, "type_info": "Text"}, {"name": "git_repo_path", "ordinal": 2, "type_info": "Text"}, {"name": "setup_script", "ordinal": 3, "type_info": "Text"}, {"name": "dev_script", "ordinal": 4, "type_info": "Text"}, {"name": "created_at!: DateTime<Utc>", "ordinal": 5, "type_info": "Text"}, {"name": "updated_at!: DateTime<Utc>", "ordinal": 6, "type_info": "Text"}], "parameters": {"Right": 0}, "nullable": [true, false, false, true, true, false, false]}, "hash": "08f2cb03665a16640d6690f29920521bae3479e3d2602e724d2c93e6fc85d8ee"}