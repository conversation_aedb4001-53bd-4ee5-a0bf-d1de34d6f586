{"db_name": "SQLite", "query": "SELECT ta.id as \"id!: Uuid\" FROM task_attempts ta \n             JOIN tasks t ON ta.task_id = t.id \n             WHERE ta.id = $1 AND t.id = $2 AND t.project_id = $3", "describe": {"columns": [{"name": "id!: <PERSON><PERSON>", "ordinal": 0, "type_info": "Blob"}], "parameters": {"Right": 3}, "nullable": [true]}, "hash": "b922df6b55164f75eec60a5ddf17a81598862a5f794d42ca89c67b4da1a25be5"}