{"db_name": "SQLite", "query": "SELECT \n                id as \"id!: Uuid\", \n                task_attempt_id as \"task_attempt_id!: Uuid\", \n                execution_process_id as \"execution_process_id!: Uuid\", \n                session_id, \n                prompt,\n                summary,\n                created_at as \"created_at!: DateTime<Utc>\", \n                updated_at as \"updated_at!: DateTime<Utc>\"\n               FROM executor_sessions \n               WHERE task_attempt_id = $1 \n               ORDER BY created_at ASC", "describe": {"columns": [{"name": "id!: <PERSON><PERSON>", "ordinal": 0, "type_info": "Blob"}, {"name": "task_attempt_id!: Uuid", "ordinal": 1, "type_info": "Blob"}, {"name": "execution_process_id!: Uuid", "ordinal": 2, "type_info": "Blob"}, {"name": "session_id", "ordinal": 3, "type_info": "Text"}, {"name": "prompt", "ordinal": 4, "type_info": "Text"}, {"name": "summary", "ordinal": 5, "type_info": "Text"}, {"name": "created_at!: DateTime<Utc>", "ordinal": 6, "type_info": "Text"}, {"name": "updated_at!: DateTime<Utc>", "ordinal": 7, "type_info": "Text"}], "parameters": {"Right": 1}, "nullable": [true, false, false, true, true, true, false, false]}, "hash": "417a8b1ff4e51de82aea0159a3b97932224dc325b23476cb84153d690227fd8b"}