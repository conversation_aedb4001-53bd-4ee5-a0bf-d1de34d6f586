{"db_name": "SQLite", "query": "\n            SELECT ta.id as \"attempt_id!: Uuid\", ta.worktree_path, p.git_repo_path as \"git_repo_path!\"\n            FROM task_attempts ta\n            JOIN tasks t ON ta.task_id = t.id\n            JOIN projects p ON t.project_id = p.id\n            WHERE ta.task_id = $1\n            ", "describe": {"columns": [{"name": "attempt_id!: <PERSON><PERSON>", "ordinal": 0, "type_info": "Blob"}, {"name": "worktree_path", "ordinal": 1, "type_info": "Text"}, {"name": "git_repo_path!", "ordinal": 2, "type_info": "Text"}], "parameters": {"Right": 1}, "nullable": [true, false, false]}, "hash": "4049ca413b285a05aca6b25385e9c8185575f01e9069e4e8581aa45d713f612f"}