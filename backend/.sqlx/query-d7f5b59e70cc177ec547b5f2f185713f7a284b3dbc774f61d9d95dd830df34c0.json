{"db_name": "SQLite", "query": "SELECT id as \"id!: Uuid\", project_id as \"project_id!: Uuid\", title, description, status as \"status!: TaskStatus\", created_at as \"created_at!: DateTime<Utc>\", updated_at as \"updated_at!: DateTime<Utc>\"\n               FROM tasks \n               WHERE id = $1", "describe": {"columns": [{"name": "id!: <PERSON><PERSON>", "ordinal": 0, "type_info": "Blob"}, {"name": "project_id!: Uuid", "ordinal": 1, "type_info": "Blob"}, {"name": "title", "ordinal": 2, "type_info": "Text"}, {"name": "description", "ordinal": 3, "type_info": "Text"}, {"name": "status!: TaskStatus", "ordinal": 4, "type_info": "Text"}, {"name": "created_at!: DateTime<Utc>", "ordinal": 5, "type_info": "Text"}, {"name": "updated_at!: DateTime<Utc>", "ordinal": 6, "type_info": "Text"}], "parameters": {"Right": 1}, "nullable": [true, false, false, true, false, false, false]}, "hash": "d7f5b59e70cc177ec547b5f2f185713f7a284b3dbc774f61d9d95dd830df34c0"}