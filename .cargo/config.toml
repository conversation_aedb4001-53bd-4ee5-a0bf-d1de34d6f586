# Set macOS deployment target to avoid linker warnings
# Use Rust project defaults: 10.12 for x86_64, 11.0 for aarch64
[target.x86_64-apple-darwin.env]
MACOSX_DEPLOYMENT_TARGET = "10.12"

[target.aarch64-apple-darwin.env]
MACOSX_DEPLOYMENT_TARGET = "11.0"

[target.x86_64-pc-windows-msvc]
rustflags = ["-C", "link-arg=/DEBUG:FASTLINK"]

[target.aarch64-pc-windows-msvc]
rustflags = ["-C", "link-arg=/DEBUG:FASTLINK"]
